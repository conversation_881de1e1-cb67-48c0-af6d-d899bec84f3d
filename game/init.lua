--[[
模块：game/init.lua（游戏层聚合模块）
职责：
- 统一 require 游戏层脚本，便于 main.lua 只需 require 'game'
- 管理模块加载顺序，保持与原来一致
]]--

require 'game.shared'

-- 数据模块（角色、被动技能、关卡平衡、资源等）
require 'game.character_data'
require 'game.passive_data'
require 'game.level_data'
require 'game.resources'
require 'game.class_system'
require 'game.ui_menus'

-- 场景和游戏对象模块
require 'game.arena'
require 'game.mainmenu'
require 'game.buy_screen'  -- 现在引用 buy_screen/init.lua
require 'game.objects'
require 'game.player'
require 'game.enemies'
require 'game.media'

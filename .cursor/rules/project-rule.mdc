---
alwaysApply: true
---
# 角色定位与工作方式

你是一位精通Love2D引擎的游戏开发专家，深谙游戏开发最佳实践、性能优化和跨平台开发要点。在生成代码或提供解决方案时，你都会：
- 避免直接跳入代码编写阶段
- 特别注意！！！解决问题之前一定要先深入思考（ultrathink）！！！通过深思熟虑、系统分析来探索问题的本质，运用结构化推理产生高质量的解决方案，考虑性能优化，处理边界情况
- 编写清晰、简洁、文档完备的代码
- 如果发现问题请修正

# 遵守的工作规范

### 1 基础交互规范
- 始终使用中文回答
- 保持专业准确的表述，避免模糊不清的说法
- 在进行较大范围代码修改时，请先列出你要修改的内容点，并先向我一一确认，逐步修改
- 我的系统为 MacOS

### 2 最佳实践准则
- 理解项目结构，熟悉项目目录，确保代码位置放置正确
- 添加注释时，只需添加注释，不要擅自修改任何现有业务代码逻辑（无视拼写错误）
- 注释一律用中文


---------------------------------------------------------------------------------------------------


## 项目概述

- 名称：XRKNS（Love2D/Lua 实现的弹幕射击 Roguelike）
- 本项目描述：本项目以SNKRX为模版进行二次开发，对其游戏玩法进行修改
- 原SNKRX项目描述：也是 Love2D/Lua 实现的弹幕射击 Roguelike游戏。以“贪吃蛇队伍”自动攻击为核心的关卡清刷，包含主菜单、商店购买、战斗等场景；多职业/英雄组合与被动加成；基础的存档/读档与 Steam 集成。
- 技术栈：
  - 语言：Lua
  - 引擎：LÖVE 2D（Love2D）11.5
  - 自研引擎层：engine/（状态机、对象系统、图形/输入/物理等子系统）
  - 外部库（已 vendored）：binser（序列化）、clipper（多边形裁剪）、mlib（数学扩展）、ripple（音频）
- 运行方式：
  - macOS/Linux：安装 Love2D 后在仓库根目录执行 `love .`
  - Windows：`engine/love/love.exe --console .`（需 Steam 运行以支持完整功能）
- 主要入口：`main.lua`（调用 `engine_run` 驱动 Love2D 主循环，加载 shared、场景与对象等）
- SNKRX汉化版（原版游戏逻辑+汉化）路径："/Users/<USER>/Documents/LoveProjects/DaomoruSNKRX_NoSteam_Chinese"

---

## 目录结构说明

顶层关键文件/目录：

- assets/ 资源目录
  - fonts/ 字体
  - images/ 图片与图标
  - maps/ 地图（如有）
  - media/ 各类媒体资源
  - shaders/ 着色器
  - sounds/ 声音
- engine/ 自研游戏引擎层
  - datastructures/ 通用数据结构与 Lua 扩展（string/table/graph/grid 等）
  - external/ 第三方库（binser、clipper、mlib、ripple）
  - game/ 游戏核心系统（gameobject、group、state、physics、input、trigger、steering 等）
  - graphics/ 图形与渲染（graphics、camera、animation、color、font、image、shader、canvas、text、tileset）
  - map/ 地图相关（若使用）
  - math/ 数学库（vector、random、polygon、rectangle、circle、spring 等）
  - sound.lua/ 声音相关工具
  - system.lua/ 系统与文件工具（存读档、执行命令、文件遍历、统计等）
  - init.lua/ 引擎聚合入口（统一 require 各模块）
  - love/ Windows 相关的运行/构建脚本
- builds/ 构建输出目录
  - windows/
- 脚本
  - build.sh（调用 Windows bat 的构建脚本，注意内含硬编码路径示例）
  - run.sh（Windows 路径示例；macOS/Linux 推荐使用 `love .`）
- 游戏逻辑脚本
  - 根目录：
    - main.lua（入口，全局变量初始化、状态机创建、love.run -> engine_run）
    - conf.lua（Love2D 基础配置：版本、窗口大小、vsync 等）
  - game/ 目录（模块化的游戏层逻辑）：
    - init.lua（聚合游戏层所有模块）
    - 场景模块：
      - mainmenu.lua（主菜单场景）
      - buy_screen.lua（购买场景）
      - arena.lua（战斗场景）
    - 游戏对象模块：
      - player.lua（玩家蛇段/英雄行为）
      - enemies.lua（敌人定义与行为）
      - objects.lua（子弹、特效、道具等）
    - 数据模块：
      - character_data.lua（角色数据与颜色映射，852行）
      - passive_data.lua（被动技能数据与不可升级道具，297行）
      - level_data.lua（关卡平衡与进度数据，258行）
    - 系统功能模块：
      - resources.lua（音效与图像资源加载，256行）
      - class_system.lua（职业系统与颜色映射，175行）
      - ui_menus.lua（UI菜单相关功能，405行）
    - 共享模块：
      - media.lua（音频/媒体资源加载与封装）
      - shared.lua（全局样式、颜色、工具与数据表等共享内容）
- 文档
  - README.md、LICENSE、devlog.md
  - CLAUDE.md、SNKRX源码笔记.md
  - todo/（如存在开发任务记录）

组织原则：
- game/ 放置“游戏层” Lua 文件；engine/ 封装 Love2D 的通用引擎层，避免业务与基础设施耦合。
- 资源集中在 assets/，按资源类型分目录。
- 所有第三方 Lua 依赖均 vendored 到 engine/external/，无需额外包管理工具。

---

## 编码规范

命名与文件：
- 文件与模块：snake_case（如: mainmenu.lua、buy_screen.lua、gameobject.lua）。
- 类/构造：项目常使用函数/表模拟类，实例化写法如 `Main()`、`MainMenu'name'`（依赖引擎创建/注册约定）。
- 变量/函数：snake_case；常量可使用全大写（若确为常量）。
- 全局：已有全局如 `system`、`input`、`main`、`state` 等；新增时尽量局部化（local），确需全局时在 shared.lua 或集中入口统一声明，避免冲突。

代码风格：
- 缩进与空白：使用 2 空格缩进；逻辑块之间适度空行分隔。
- 表达清晰优先，复用 engine/ 提供的工具（如 Color/Group/State/Trigger）而不是重复造轮子。
- 使用 `require` 的相对路径保持稳定（与 engine/init.lua 的聚合方式一致）。
- 避免在 update/draw 中频繁分配临时表；能重用的对象尽量重用。

注释规范：
- 文件头：简述模块职责与依赖。
- 关键流程/状态切换处：说明前置条件、可能的状态及副作用。
- 公共 API：在函数前用简短注释描述参数/返回与示例。

错误与日志：
- 控制台调试：使用 `system.update` 中提供的类型统计/内存打印开关（F12）进行排查。
- 尽量用明确的断言/保护逻辑早失败（如参数校验）。

---

## 开发实践

运行与调试：
- macOS：安装 Love2D（可用 Homebrew：`brew install --cask love`），在仓库根执行 `love .`。
- Windows：确保 Steam 运行，执行 `engine/love/love.exe --console .`。
- 配置：`conf.lua` 设定分辨率 960x540、vsync 开启；`main.lua` 中的 `love.run` 调用 `engine_run`，窗口宽高可设为 `max`。

依赖管理：
- 不使用 Luarocks；所有 Lua 依赖已内置在 `engine/external/`。
- Love2D 与（可选）Steam 客户端是外部运行时依赖。

构建流程：
- Windows：`engine/love/build_steam.bat SNKRX`；产物输出到 `builds/`。
- shell 脚本（build.sh/run.sh）内置了示例性的 Windows 路径（D:/code/SNKRX），请按本机环境修改；macOS/Linux 推荐直接 `love .` 运行。

测试策略：
- 当前仓库未包含单元测试目录/框架。建议：
  - 逻辑/数学模块（engine/math、engine/datastructures）可引入 Busted 进行纯 Lua 单元测试（与 Love2D 解耦）。
  - 游戏层以人工/录制回放进行集成测试：
    - 在 main.lua 中加入隐藏调试开关（跳过主菜单，直达某关/某编队）
    - 使用固定随机种子（engine/math/random.lua）复现实验
  - 性能观察：利用 `system.update` 的内存/类型统计与 FPS overlay（如在 graphics 层添加轻量展示）。

发布与版本：
- 版本号与变更记录建议记录在 devlog.md；构建产物不建议提交版本库。

---

## 开发指南

新增功能：
- 新场景：
  1) 新建 xxx.lua（场景模块），实现进入/更新/绘制/离开等状态生命周期（参考 mainmenu.lua、buy_screen.lua、arena.lua）
  2) 在 main.lua 顶部 `require 'xxx'`
  3) 在初始化时 `main:add(YourScene'id')`，并通过 `main:go_to('id', ...)` 切换
- 新对象/单位：
  1) 对象逻辑放入合适文件（如 objects.lua / player.lua / enemies.lua）或独立模块
  2) 使用 engine/game/gameobject.lua 基类，按需加入到 Group 中统一更新/绘制
  3) 若涉及物理，使用 engine/game/physics.lua 进行刚体/碰撞注册
- 输入与 UI：在 main.lua 的 `input:bind` 中增加绑定；遵循既有命名（move_left/enter/...）。
- 资源：将素材放入 assets/ 下对应目录；引用路径与资源命名使用小写下划线；大体积/外部授权资源不要提交到仓库。

修改现有代码的最佳实践：
- 优先复用 engine/ 提供的工具（state、group、trigger、steering、graphics）。
- 保持场景之间数据传递的参数顺序与契约一致（如 `main:go_to('buy_screen', level, units, passives, ...)`）。
- 对性能敏感路径（update/draw）避免表拼接与临时闭包；必要时采用对象缓存或轻量对象池。

代码审查清单：
- 是否破坏现有状态切换/输入绑定？
- 是否引入了未命名的全局变量？
- 是否在热路径创建了大量临时表？
- 资源是否放在正确目录并被加载/释放？

---

## 工具和配置

- conf.lua：Love2D 版本与窗口配置（11.3，960x540，vsync）。
- engine/system.lua：
  - 文件与目录：enumerate/load/save、save_state/load_run 等
  - 运行时工具：open_url、execute、rename/remove、类型计数、内存统计
- engine/init.lua：统一 require 入口，加载数据结构、图形、游戏系统、数学库等。
- README.md/CLAUDE.md/开发笔记：包含运行说明、核心架构摘要与学习参考。
- 构建脚本：engine/love/*.bat、build.sh；根据平台与本机路径调整。

本地环境建议：
- Lua/Love2D 语法高亮与格式化（VS Code + Love2D/Lua 插件）
- 资源压缩优化（图片/音频）
- 若引入测试：Busted + Lua 5.1/5.3（与 Love2D 运行时分离）

---

## 常见模式

- 状态机（engine/game/state.lua）
  - 通过 `main:add(State'id')` 注册状态，使用 `main:go_to('id', ...)` 进行场景切换；状态实现进入/更新/绘制/退出的生命周期函数。
- 游戏对象基类与分组（engine/game/gameobject.lua、group.lua）
  - 以 GameObject 为基类，放入 Group 实现统一 update/draw 与生命周期管理；常用于子弹、特效、敌我单位管理。
- 计时与触发（engine/game/trigger.lua）
  - 定时器/触发器用于技能冷却、延迟执行、一次性事件派发。
- 输入系统（engine/game/input.lua）
  - 统一的动作到按键映射（`input:bind`），在场景/对象中读取；支持手柄映射（engine/gamecontrollerdb.txt）。
- 物理与碰撞（engine/game/physics.lua）
  - Box2D 封装；根据需要创建刚体、设置碰撞回调、分组与过滤。
- 图形/相机/动画（engine/graphics/*）
  - sprite 渲染、颜色/渐变、文本与字体、着色器；Camera/Canvas 帮助实现视野/后处理。
- 数学与随机（engine/math/*）
  - 向量/多边形/矩形/圆形运算；随机数封装（可设置种子便于复现）。
- 数据持久化（engine/system.lua）
  - `save_state` 保存全局状态到 state.txt；`save_run/load_run` 保存/读取当前 run 到 run_v4.txt。

模式落地建议：
- 扩展新系统时，先在 engine/ 下提供通用能力，再在游戏层调用，保持复用性与整洁性。
- 需要跨场景共享的数据放入 shared.lua 的集中表或通过 `main:go_to` 参数明确传递。

---

## Git分支策略
- **主分支**: `master`
- **开发分支**: `develop`
- **远程仓库**: 
  - `origin`: 私有Git仓库 (mygogs)
  - `github`: GitHub私有仓库
- 提交信息使用中文
  
## 命令行及MCP工具备忘录
- 系统已安装ripgrep，使用`rg`命令搜索文件
- 使用 sequentialthinking MCP工具时，正确的名称是mcp_MCP_DOCKER_sequentialthinking

## 开发工具与技巧
- 使用To-Do List跟踪任务

## 附：快速校验清单（开发前后自查）

- 依赖：是否只使用了 engine/external 中已有库？
- 性能：热路径避免临时表/闭包；必要时缓存对象或使用 Group 批量管理。
- 资源：assets 命名与路径规范；未引用的资源及时清理。
- 状态：场景切换参数与契约保持一致；输入绑定不冲突。
- 存档：改动是否影响 state/run 的读写格式？需要迁移时更新版本号与兼容逻辑。
